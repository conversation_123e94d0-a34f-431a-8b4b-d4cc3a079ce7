const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
require('dotenv').config();

// 导入配置和中间件
const { getConfig, getLogFilePath, checkFileSize } = require('./config');
const {
  validateKey,
  connectionLimit,
  createRateLimit,
  requestLogger,
  errorHandler,
  getConnectionStats
} = require('./middleware/security');

const app = express();
const config = getConfig();

// 中间件设置
app.use(cors());
app.use(express.json());
app.use(requestLogger());

// 速率限制
app.use('/tail', createRateLimit(config.security.rateLimitWindow, config.security.rateLimitMax));

// 存储活跃的连接
const activeConnections = new Map();

/**
 * 获取日志文件路径并验证
 */
function getValidatedLogFilePath(logKey) {
  const filePath = getLogFilePath(logKey, config);

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    throw new Error(`Log file not found: ${filePath}`);
  }

  // 检查文件大小
  if (!checkFileSize(filePath, config)) {
    throw new Error(`Log file too large: ${filePath}`);
  }

  return filePath;
}

/**
 * 读取文件的最后 N 行
 */
function readLastLines(filePath, lines = 50) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    const allLines = data.split('\n');
    const lastLines = allLines.slice(-lines).filter(line => line.trim() !== '');
    return lastLines;
  } catch (error) {
    console.error('Error reading file:', error);
    return [];
  }
}

/**
 * 设置文件监控
 */
function setupFileWatcher(filePath, res, connectionId) {
  const watcher = chokidar.watch(filePath, {
    persistent: true,
    usePolling: true,
    interval: 1000
  });

  let lastSize = 0;
  try {
    lastSize = fs.statSync(filePath).size;
  } catch (error) {
    console.error('Error getting file stats:', error);
  }

  watcher.on('change', () => {
    try {
      const stats = fs.statSync(filePath);
      const currentSize = stats.size;
      
      if (currentSize > lastSize) {
        // 文件增长，读取新内容
        const stream = fs.createReadStream(filePath, {
          start: lastSize,
          end: currentSize - 1,
          encoding: 'utf8'
        });
        
        let newContent = '';
        stream.on('data', (chunk) => {
          newContent += chunk;
        });
        
        stream.on('end', () => {
          const newLines = newContent.split('\n').filter(line => line.trim() !== '');
          newLines.forEach(line => {
            if (res.writable) {
              res.write(`data: ${JSON.stringify({ line, timestamp: new Date().toISOString() })}\n\n`);
            }
          });
        });
        
        lastSize = currentSize;
      } else if (currentSize < lastSize) {
        // 文件被截断或重新创建
        lastSize = 0;
        if (res.writable) {
          res.write(`data: ${JSON.stringify({ 
            line: '[LOG FILE ROTATED OR TRUNCATED]', 
            timestamp: new Date().toISOString() 
          })}\n\n`);
        }
      }
    } catch (error) {
      console.error('Error watching file:', error);
      if (res.writable) {
        res.write(`data: ${JSON.stringify({ 
          line: `[ERROR] ${error.message}`, 
          timestamp: new Date().toISOString() 
        })}\n\n`);
      }
    }
  });

  watcher.on('error', (error) => {
    console.error('Watcher error:', error);
    if (res.writable) {
      res.write(`data: ${JSON.stringify({ 
        line: `[WATCHER ERROR] ${error.message}`, 
        timestamp: new Date().toISOString() 
      })}\n\n`);
    }
  });

  // 存储连接信息
  activeConnections.set(connectionId, {
    response: res,
    watcher: watcher,
    filePath: filePath,
    startTime: new Date()
  });

  return watcher;
}

// POST 路由：开始监控日志文件
app.post('/tail/:logKey',
  validateKey(config.security.validKeys),
  connectionLimit(config.security.maxConnections),
  (req, res) => {
    const { logKey } = req.params;

    try {
      // 获取日志文件路径
      const filePath = getValidatedLogFilePath(logKey);
    
    // 生成连接ID
    const connectionId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 设置 Server-Sent Events 头部
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 发送连接建立消息
    res.write(`data: ${JSON.stringify({ 
      line: `[CONNECTED] Monitoring ${filePath}`, 
      timestamp: new Date().toISOString(),
      connectionId: connectionId
    })}\n\n`);

    // 发送最近的日志行
    const recentLines = readLastLines(filePath, 20);
    recentLines.forEach(line => {
      res.write(`data: ${JSON.stringify({ 
        line: `[HISTORY] ${line}`, 
        timestamp: new Date().toISOString() 
      })}\n\n`);
    });

    // 设置文件监控
    const watcher = setupFileWatcher(filePath, res, connectionId);

    // 处理客户端断开连接
    req.on('close', () => {
      console.log(`Client disconnected: ${connectionId}`);
      if (activeConnections.has(connectionId)) {
        const connection = activeConnections.get(connectionId);
        connection.watcher.close();
        activeConnections.delete(connectionId);
      }
    });

    // 发送心跳
    const heartbeat = setInterval(() => {
      if (res.writable) {
        res.write(`data: ${JSON.stringify({ 
          line: '[HEARTBEAT]', 
          timestamp: new Date().toISOString() 
        })}\n\n`);
      } else {
        clearInterval(heartbeat);
      }
    }, 30000); // 每30秒发送一次心跳

  } catch (error) {
    console.error('Error setting up log monitoring:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET 路由：获取活跃连接信息
app.get('/status', (req, res) => {
  const connections = Array.from(activeConnections.entries()).map(([id, conn]) => ({
    connectionId: id,
    filePath: conn.filePath,
    startTime: conn.startTime,
    duration: Date.now() - conn.startTime.getTime()
  }));

  const connectionStats = getConnectionStats();

  res.json({
    activeConnections: connections.length,
    connections: connections,
    availableLogs: Object.keys(config.logs.paths),
    connectionStats: connectionStats,
    config: {
      maxConnections: config.security.maxConnections,
      rateLimitWindow: config.security.rateLimitWindow,
      rateLimitMax: config.security.rateLimitMax
    }
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 添加错误处理中间件
app.use(errorHandler());

// 启动服务器
app.listen(config.server.port, config.server.host, () => {
  console.log(`Log tail server running on ${config.server.host}:${config.server.port}`);
  console.log(`Available log keys: ${Object.keys(config.logs.paths).join(', ')}`);
  console.log(`Valid access keys: ${config.security.validKeys.length} configured`);
  console.log(`Max connections per IP: ${config.security.maxConnections}`);
  console.log(`Rate limit: ${config.security.rateLimitMax} requests per ${config.security.rateLimitWindow}ms`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\nShutting down gracefully...');
  
  // 关闭所有活跃连接
  activeConnections.forEach((connection) => {
    connection.watcher.close();
    if (connection.response.writable) {
      connection.response.end();
    }
  });
  
  process.exit(0);
});
