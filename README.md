# BJ Tail Log - 实时日志监控服务

一个基于 Node.js 的实时日志文件监控服务，使用 Server-Sent Events (SSE) 技术实现类似 `tail -f` 的功能。

## 功能特性

- 🔄 **实时监控**: 使用 Server-Sent Events 实现实时日志流
- 🔐 **安全验证**: 基于密钥的访问控制
- 📊 **多文件支持**: 支持监控多个不同类型的日志文件
- 🚀 **高性能**: 使用 chokidar 进行高效文件监控
- 📈 **连接管理**: 支持多客户端同时连接
- 🛡️ **速率限制**: 防止滥用的速率限制机制
- 💓 **心跳检测**: 自动心跳保持连接活跃

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env` 文件并根据需要修改配置：

```bash
# 服务器端口
PORT=3000

# 有效的访问密钥（用逗号分隔）
VALID_KEYS=secret-key-1,secret-key-2,admin-key
```

### 3. 启动服务

```bash
# 生产环境
npm start

# 开发环境（自动重启）
npm run dev
```

## API 接口

### 开始监控日志文件

**POST** `/tail/:logKey`

开始监控指定的日志文件，返回 Server-Sent Events 流。

#### 参数

- `logKey` (路径参数): 日志文件标识符
  - `app` - 应用日志
  - `error` - 错误日志  
  - `access` - 访问日志
  - `debug` - 调试日志

#### 请求体

```json
{
  "key": "your-access-key"
}
```

#### 响应

返回 `text/event-stream` 格式的数据流：

```
data: {"line": "[INFO] Application started", "timestamp": "2025-08-04T09:35:01.000Z"}

data: {"line": "[INFO] New log entry", "timestamp": "2025-08-04T09:35:02.000Z"}
```

#### 示例

```bash
curl -X POST http://localhost:3000/tail/app \
  -H "Content-Type: application/json" \
  -d '{"key": "secret-key-1"}'
```

### 获取服务状态

**GET** `/status`

获取当前服务状态和活跃连接信息。

#### 响应

```json
{
  "activeConnections": 2,
  "connections": [
    {
      "connectionId": "1725429301234_abc123",
      "filePath": "/path/to/logs/app.log",
      "startTime": "2025-08-04T09:35:01.234Z",
      "duration": 30000
    }
  ],
  "availableLogs": ["app", "error", "access", "debug"]
}
```

### 健康检查

**GET** `/health`

简单的健康检查接口。

#### 响应

```json
{
  "status": "ok",
  "timestamp": "2025-08-04T09:35:01.000Z"
}
```

## 配置说明

### 日志文件配置

在 `config.js` 中可以配置日志文件路径：

```javascript
logs: {
  paths: {
    'app': './logs/app.log',
    'error': './logs/error.log',
    'access': './logs/access.log',
    'debug': './logs/debug.log'
  }
}
```

### 安全配置

- **访问密钥**: 在 `.env` 文件中配置 `VALID_KEYS`
- **连接限制**: 每个 IP 的最大连接数
- **速率限制**: 防止频繁请求

## 测试工具

项目包含了测试脚本 `test.js`：

### 模拟日志写入

```bash
node test.js simulate
```

启动日志模拟器，每2秒向随机日志文件写入一条记录。

### 测试连接

```bash
node test.js test
```

测试连接到日志流并显示实时数据。

### 查看状态

```bash
node test.js status
```

获取服务器当前状态。

## 客户端示例

### JavaScript (浏览器)

```javascript
// 发起 POST 请求获取 EventSource URL
fetch('/tail/app', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    key: 'your-access-key'
  })
})
.then(response => {
  if (!response.ok) {
    throw new Error('Failed to connect');
  }
  
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  function readStream() {
    reader.read().then(({ done, value }) => {
      if (done) return;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      lines.forEach(line => {
        if (line.startsWith('data: ')) {
          const data = JSON.parse(line.substring(6));
          console.log(`[${data.timestamp}] ${data.line}`);
        }
      });
      
      readStream();
    });
  }
  
  readStream();
});
```

### Python

```python
import requests
import json

response = requests.post(
    'http://localhost:3000/tail/app',
    json={'key': 'secret-key-1'},
    stream=True
)

for line in response.iter_lines():
    if line.startswith(b'data: '):
        data = json.loads(line[6:])
        print(f"[{data['timestamp']}] {data['line']}")
```

## 部署建议

### 生产环境

1. **使用进程管理器**:
   ```bash
   npm install -g pm2
   pm2 start server.js --name "bj-tail-log"
   ```

2. **反向代理** (Nginx):
   ```nginx
   location /tail {
       proxy_pass http://localhost:3000;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection 'upgrade';
       proxy_set_header Host $host;
       proxy_cache_bypass $http_upgrade;
       proxy_buffering off;
   }
   ```

3. **环境变量**:
   ```bash
   export NODE_ENV=production
   export VALID_KEYS=prod-key-1,prod-key-2
   export PORT=3000
   ```

### Docker 部署

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000
CMD ["npm", "start"]
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查访问密钥是否正确
   - 确认服务器正在运行

2. **日志文件不存在**
   - 确保日志文件路径正确
   - 检查文件权限

3. **连接断开**
   - 检查网络连接
   - 查看服务器日志

### 日志级别

服务器会输出详细的连接和错误日志，帮助诊断问题。

## 许可证

MIT License
