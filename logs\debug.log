2025-08-04 09:35:01 [DEBUG] Initializing application modules
2025-08-04 09:35:02 [DEBUG] Loading configuration from config.json
2025-08-04 09:35:03 [DEBUG] Setting up middleware stack
2025-08-04 09:35:04 [DEBUG] Registering route handlers
2025-08-04 09:35:05 [DEBUG] Starting background workers
2025-08-04 09:35:10 [DEBUG] Processing authentication request
2025-08-04 09:35:11 [DEBUG] Validating user credentials
2025-08-04 09:35:12 [DEBUG] Generating JWT token
2025-08-04 09:35:15 [DEBUG] Executing database query: SELECT * FROM users WHERE id = ?
2025-08-04 01:42:57 [DEBUG] Validating request parameters
2025-08-04 01:43:15 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:43:27 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:43:29 [DEBUG] Generating response payload
2025-08-04 01:43:31 [DEBUG] Validating request parameters
2025-08-04 01:43:35 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:43:37 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:43:41 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:43:47 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:43:49 [DEBUG] Generating response payload
2025-08-04 01:43:51 [DEBUG] Generating response payload
2025-08-04 01:43:55 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:44:06 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:44:08 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:44:22 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:44:30 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:44:36 [DEBUG] Validating request parameters
2025-08-04 01:44:38 [DEBUG] Validating request parameters
2025-08-04 01:45:00 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:45:56 [DEBUG] Validating request parameters
2025-08-04 01:45:58 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:46:02 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:46:20 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:46:28 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:46:36 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:46:44 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:46:48 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:46:58 [DEBUG] Generating response payload
2025-08-04 01:47:18 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:47:24 [DEBUG] Validating request parameters
2025-08-04 01:47:30 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:48:13 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:48:19 [DEBUG] Generating response payload
2025-08-04 01:48:37 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:48:43 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:48:49 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:48:59 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:49:01 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:49:03 [DEBUG] Validating request parameters
2025-08-04 01:49:11 [DEBUG] Validating request parameters
2025-08-04 01:49:15 [DEBUG] Generating response payload
2025-08-04 01:49:17 [DEBUG] Processing webhook payload: order.completed
2025-08-04 01:49:35 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:49:49 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:49:53 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:49:57 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:50:01 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:50:03 [DEBUG] Validating request parameters
2025-08-04 01:50:31 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:50:33 [DEBUG] Generating response payload
2025-08-04 01:50:37 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:50:41 [DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?
2025-08-04 01:50:51 [DEBUG] Cache miss for key: user_profile_123
2025-08-04 01:50:53 [DEBUG] Generating response payload
2025-08-04 01:51:01 [DEBUG] Processing webhook payload: order.completed
