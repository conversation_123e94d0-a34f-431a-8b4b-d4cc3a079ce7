{"name": "bj-tail-log", "version": "1.0.0", "description": "A Node.js service for real-time log file monitoring with Server-Sent Events", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test.js"}, "keywords": ["log", "tail", "server-sent-events", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"chokidar": "^3.5.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}