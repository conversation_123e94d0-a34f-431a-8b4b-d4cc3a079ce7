const fs = require('fs');
const path = require('path');

/**
 * 测试脚本：模拟日志文件写入
 */

const logFiles = [
  './logs/app.log',
  './logs/error.log',
  './logs/access.log',
  './logs/debug.log'
];

const logMessages = {
  app: [
    '[INFO] User authentication successful',
    '[INFO] Processing payment transaction',
    '[INFO] Email notification sent',
    '[INFO] <PERSON><PERSON> updated successfully',
    '[INFO] Background job completed'
  ],
  error: [
    '[ERROR] Database connection timeout',
    '[ERROR] Payment gateway error: insufficient funds',
    '[WARN] High CPU usage detected: 90%',
    '[ERROR] File upload failed: file too large',
    '[WARN] Memory usage warning: 85%'
  ],
  access: [
    '192.168.1.105 - - [04/Aug/2025:09:40:01 +0800] "GET /api/products HTTP/1.1" 200 3456',
    '192.168.1.106 - - [04/Aug/2025:09:40:05 +0800] "POST /api/orders HTTP/1.1" 201 789',
    '192.168.1.107 - - [04/Aug/2025:09:40:10 +0800] "PUT /api/users/789 HTTP/1.1" 200 456',
    '192.168.1.108 - - [04/Aug/2025:09:40:15 +0800] "DELETE /api/orders/123 HTTP/1.1" 204 0',
    '192.168.1.109 - - [04/Aug/2025:09:40:20 +0800] "GET /api/reports HTTP/1.1" 500 234'
  ],
  debug: [
    '[DEBUG] Executing SQL query: SELECT * FROM orders WHERE status = ?',
    '[DEBUG] Cache miss for key: user_profile_123',
    '[DEBUG] Processing webhook payload: order.completed',
    '[DEBUG] Validating request parameters',
    '[DEBUG] Generating response payload'
  ]
};

function getCurrentTimestamp() {
  return new Date().toISOString().replace('T', ' ').substring(0, 19);
}

function getRandomMessage(type) {
  const messages = logMessages[type];
  return messages[Math.floor(Math.random() * messages.length)];
}

function appendToLogFile(filePath, message) {
  const timestamp = getCurrentTimestamp();
  const logEntry = `${timestamp} ${message}\n`;
  
  fs.appendFileSync(filePath, logEntry);
  console.log(`Added to ${path.basename(filePath)}: ${message}`);
}

function simulateLogActivity() {
  console.log('Starting log simulation...');
  console.log('Press Ctrl+C to stop\n');
  
  const interval = setInterval(() => {
    // 随机选择一个日志文件
    const logTypes = ['app', 'error', 'access', 'debug'];
    const randomType = logTypes[Math.floor(Math.random() * logTypes.length)];
    const filePath = `./logs/${randomType}.log`;
    
    // 获取随机消息
    const message = getRandomMessage(randomType);
    
    // 写入日志文件
    try {
      appendToLogFile(filePath, message);
    } catch (error) {
      console.error(`Error writing to ${filePath}:`, error.message);
    }
    
  }, 2000); // 每2秒写入一条日志
  
  // 优雅退出
  process.on('SIGINT', () => {
    console.log('\nStopping log simulation...');
    clearInterval(interval);
    process.exit(0);
  });
}

// 测试函数：发送HTTP请求
async function testTailEndpoint() {
  console.log('Testing tail endpoint...\n');
  
  try {
    const response = await fetch('http://localhost:3000/tail/app', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        key: 'secret-key-1'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    console.log('Connected to log stream. Listening for events...\n');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('Stream ended');
        break;
      }
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.substring(6));
            console.log(`[${data.timestamp}] ${data.line}`);
          } catch (error) {
            console.log('Raw data:', line);
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error testing endpoint:', error.message);
  }
}

// 命令行参数处理
const command = process.argv[2];

switch (command) {
  case 'simulate':
    simulateLogActivity();
    break;
  case 'test':
    testTailEndpoint();
    break;
  case 'status':
    fetch('http://localhost:3000/status')
      .then(response => response.json())
      .then(data => {
        console.log('Server Status:');
        console.log(JSON.stringify(data, null, 2));
      })
      .catch(error => {
        console.error('Error getting status:', error.message);
      });
    break;
  default:
    console.log('Usage:');
    console.log('  node test.js simulate  - Start log simulation');
    console.log('  node test.js test      - Test tail endpoint');
    console.log('  node test.js status    - Get server status');
    break;
}
