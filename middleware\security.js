const rateLimit = require('express-rate-limit');

// 存储连接计数
const connectionCounts = new Map();

/**
 * 验证访问密钥中间件
 */
function validateKey(validKeys) {
  return (req, res, next) => {
    const { key } = req.body;
    
    if (!key) {
      return res.status(400).json({ 
        error: 'Access key is required',
        code: 'MISSING_KEY'
      });
    }
    
    if (!validKeys.includes(key)) {
      return res.status(401).json({ 
        error: 'Invalid access key',
        code: 'INVALID_KEY'
      });
    }
    
    next();
  };
}

/**
 * 连接数限制中间件
 */
function connectionLimit(maxConnections) {
  return (req, res, next) => {
    const clientIp = req.ip || req.connection.remoteAddress;
    const currentConnections = connectionCounts.get(clientIp) || 0;
    
    if (currentConnections >= maxConnections) {
      return res.status(429).json({ 
        error: 'Too many connections from this IP',
        code: 'CONNECTION_LIMIT_EXCEEDED',
        maxConnections: maxConnections
      });
    }
    
    // 增加连接计数
    connectionCounts.set(clientIp, currentConnections + 1);
    
    // 请求结束时减少连接计数
    res.on('finish', () => {
      const count = connectionCounts.get(clientIp) || 0;
      if (count <= 1) {
        connectionCounts.delete(clientIp);
      } else {
        connectionCounts.set(clientIp, count - 1);
      }
    });
    
    next();
  };
}

/**
 * 创建速率限制中间件
 */
function createRateLimit(windowMs, max) {
  return rateLimit({
    windowMs: windowMs,
    max: max,
    message: {
      error: 'Too many requests from this IP',
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // 跳过健康检查和状态接口的速率限制
      return req.path === '/health' || req.path === '/status';
    }
  });
}

/**
 * 请求日志中间件
 */
function requestLogger() {
  return (req, res, next) => {
    const start = Date.now();
    const clientIp = req.ip || req.connection.remoteAddress;
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      console.log(`${new Date().toISOString()} [${req.method}] ${req.path} - ${clientIp} - ${res.statusCode} (${duration}ms)`);
    });
    
    next();
  };
}

/**
 * 错误处理中间件
 */
function errorHandler() {
  return (err, req, res, next) => {
    console.error('Error:', err);
    
    if (res.headersSent) {
      return next(err);
    }
    
    const statusCode = err.statusCode || 500;
    const message = err.message || 'Internal Server Error';
    
    res.status(statusCode).json({
      error: message,
      code: err.code || 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  };
}

/**
 * 获取当前连接统计
 */
function getConnectionStats() {
  const totalConnections = Array.from(connectionCounts.values()).reduce((sum, count) => sum + count, 0);
  const uniqueIPs = connectionCounts.size;
  
  return {
    totalConnections,
    uniqueIPs,
    connectionsByIP: Object.fromEntries(connectionCounts)
  };
}

module.exports = {
  validateKey,
  connectionLimit,
  createRateLimit,
  requestLogger,
  errorHandler,
  getConnectionStats
};
