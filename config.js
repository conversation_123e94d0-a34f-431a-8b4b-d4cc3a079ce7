const path = require('path');

// 默认配置
const defaultConfig = {
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || '0.0.0.0'
  },
  
  security: {
    validKeys: process.env.VALID_KEYS ? process.env.VALID_KEYS.split(',') : ['default-key'],
    maxConnections: parseInt(process.env.MAX_CONNECTIONS) || 100,
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000, // 1分钟
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 10 // 每分钟最多10次请求
  },
  
  monitoring: {
    watchInterval: parseInt(process.env.WATCH_INTERVAL) || 1000,
    heartbeatInterval: parseInt(process.env.HEARTBEAT_INTERVAL) || 30000,
    maxHistoryLines: parseInt(process.env.MAX_HISTORY_LINES) || 50,
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 100 * 1024 * 1024 // 100MB
  },
  
  logs: {
    // 日志文件路径配置
    paths: {
      'app': process.env.LOG_APP_PATH || './logs/app.log',
      'error': process.env.LOG_ERROR_PATH || './logs/error.log',
      'access': process.env.LOG_ACCESS_PATH || './logs/access.log',
      'debug': process.env.LOG_DEBUG_PATH || './logs/debug.log'
    },
    
    // 允许的日志文件扩展名
    allowedExtensions: ['.log', '.txt'],
    
    // 日志文件大小限制检查
    enableSizeCheck: process.env.ENABLE_SIZE_CHECK !== 'false'
  }
};

/**
 * 验证配置
 */
function validateConfig(config) {
  const errors = [];
  
  // 验证端口
  if (config.server.port < 1 || config.server.port > 65535) {
    errors.push('Invalid port number');
  }
  
  // 验证访问密钥
  if (!config.security.validKeys || config.security.validKeys.length === 0) {
    errors.push('No valid access keys configured');
  }
  
  // 验证日志路径
  Object.entries(config.logs.paths).forEach(([key, logPath]) => {
    const fullPath = path.resolve(logPath);
    const ext = path.extname(fullPath);
    
    if (!config.logs.allowedExtensions.includes(ext)) {
      errors.push(`Invalid file extension for log '${key}': ${ext}`);
    }
  });
  
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
  
  return true;
}

/**
 * 获取配置
 */
function getConfig() {
  try {
    validateConfig(defaultConfig);
    return defaultConfig;
  } catch (error) {
    console.error('Configuration error:', error.message);
    process.exit(1);
  }
}

/**
 * 获取日志文件的完整路径
 */
function getLogFilePath(logKey, config = defaultConfig) {
  const logPath = config.logs.paths[logKey];
  if (!logPath) {
    throw new Error(`Unknown log key: ${logKey}`);
  }
  
  return path.resolve(logPath);
}

/**
 * 检查文件大小是否超过限制
 */
function checkFileSize(filePath, config = defaultConfig) {
  if (!config.logs.enableSizeCheck) {
    return true;
  }
  
  try {
    const fs = require('fs');
    const stats = fs.statSync(filePath);
    return stats.size <= config.monitoring.maxFileSize;
  } catch (error) {
    return false;
  }
}

module.exports = {
  getConfig,
  getLogFilePath,
  checkFileSize,
  validateConfig
};
