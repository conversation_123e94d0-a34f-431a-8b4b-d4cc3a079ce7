<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时日志监控</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background-color: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        label {
            font-size: 12px;
            color: #cccccc;
        }
        
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #3c3c3c;
            background-color: #2d2d30;
            color: #d4d4d4;
            border-radius: 4px;
            font-family: inherit;
        }
        
        button {
            background-color: #0e639c;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background-color: #1177bb;
        }
        
        button:disabled {
            background-color: #3c3c3c;
            cursor: not-allowed;
        }
        
        .status {
            background-color: #2d2d30;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 12px;
        }
        
        .log-container {
            background-color: #0d1117;
            border: 1px solid #3c3c3c;
            border-radius: 8px;
            height: 500px;
            overflow-y: auto;
            padding: 15px;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 2px;
            word-wrap: break-word;
        }
        
        .log-entry.info {
            color: #4fc1ff;
        }
        
        .log-entry.error {
            color: #f85149;
        }
        
        .log-entry.warn {
            color: #d29922;
        }
        
        .log-entry.debug {
            color: #a5a5a5;
        }
        
        .log-entry.system {
            color: #7c3aed;
            font-style: italic;
        }
        
        .timestamp {
            color: #6e7681;
            margin-right: 8px;
        }
        
        .clear-btn {
            background-color: #da3633;
        }
        
        .clear-btn:hover {
            background-color: #f85149;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 实时日志监控</h1>
            <p>连接到服务器查看实时日志流</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="serverUrl">服务器地址</label>
                <input type="text" id="serverUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
            </div>
            
            <div class="control-group">
                <label for="logType">日志类型</label>
                <select id="logType">
                    <option value="app">应用日志 (app)</option>
                    <option value="error">错误日志 (error)</option>
                    <option value="access">访问日志 (access)</option>
                    <option value="debug">调试日志 (debug)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="accessKey">访问密钥</label>
                <input type="password" id="accessKey" value="secret-key-1" placeholder="输入访问密钥">
            </div>
            
            <div class="control-group">
                <label>&nbsp;</label>
                <button id="connectBtn" onclick="toggleConnection()">连接</button>
            </div>
            
            <div class="control-group">
                <label>&nbsp;</label>
                <button class="clear-btn" onclick="clearLogs()">清空日志</button>
            </div>
        </div>
        
        <div class="status" id="status">
            状态: 未连接
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry system">
                <span class="timestamp">[系统]</span>
                点击"连接"按钮开始监控日志...
            </div>
        </div>
    </div>

    <script>
        let isConnected = false;
        let reader = null;
        let abortController = null;
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `状态: ${message} <span style="color: #6e7681;">[${timestamp}]</span>`;
        }
        
        function addLogEntry(line, timestamp, type = 'info') {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const time = new Date(timestamp).toLocaleTimeString();
            entry.innerHTML = `<span class="timestamp">[${time}]</span>${escapeHtml(line)}`;
            
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
            
            // 限制日志条数，避免内存占用过多
            const entries = container.querySelectorAll('.log-entry');
            if (entries.length > 1000) {
                entries[0].remove();
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function getLogType(line) {
            if (line.includes('[ERROR]')) return 'error';
            if (line.includes('[WARN]')) return 'warn';
            if (line.includes('[DEBUG]')) return 'debug';
            if (line.includes('[INFO]')) return 'info';
            if (line.includes('[CONNECTED]') || line.includes('[HEARTBEAT]') || line.includes('[HISTORY]')) return 'system';
            return 'info';
        }
        
        async function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            const logType = document.getElementById('logType').value;
            const accessKey = document.getElementById('accessKey').value;
            
            if (!serverUrl || !accessKey) {
                alert('请填写服务器地址和访问密钥');
                return;
            }
            
            try {
                abortController = new AbortController();
                updateStatus('正在连接...', 'info');
                
                const response = await fetch(`${serverUrl}/tail/${logType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        key: accessKey
                    }),
                    signal: abortController.signal
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || `HTTP ${response.status}`);
                }
                
                updateStatus(`已连接到 ${logType} 日志`, 'info');
                isConnected = true;
                updateUI();
                
                reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        updateStatus('连接已断开', 'warn');
                        break;
                    }
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.substring(6));
                                const logType = getLogType(data.line);
                                addLogEntry(data.line, data.timestamp, logType);
                            } catch (error) {
                                console.error('解析数据失败:', error);
                                addLogEntry(line, new Date().toISOString(), 'error');
                            }
                        }
                    }
                }
                
            } catch (error) {
                if (error.name !== 'AbortError') {
                    updateStatus(`连接失败: ${error.message}`, 'error');
                    addLogEntry(`连接错误: ${error.message}`, new Date().toISOString(), 'error');
                }
            } finally {
                isConnected = false;
                updateUI();
            }
        }
        
        function disconnect() {
            if (abortController) {
                abortController.abort();
            }
            if (reader) {
                reader.cancel();
            }
            isConnected = false;
            updateStatus('已断开连接', 'info');
            updateUI();
        }
        
        function toggleConnection() {
            if (isConnected) {
                disconnect();
            } else {
                connect();
            }
        }
        
        function updateUI() {
            const connectBtn = document.getElementById('connectBtn');
            const inputs = document.querySelectorAll('input, select');
            
            connectBtn.textContent = isConnected ? '断开' : '连接';
            connectBtn.style.backgroundColor = isConnected ? '#da3633' : '#0e639c';
            
            inputs.forEach(input => {
                input.disabled = isConnected;
            });
        }
        
        function clearLogs() {
            const container = document.getElementById('logContainer');
            container.innerHTML = '<div class="log-entry system"><span class="timestamp">[系统]</span>日志已清空</div>';
        }
        
        // 页面关闭时断开连接
        window.addEventListener('beforeunload', () => {
            if (isConnected) {
                disconnect();
            }
        });
    </script>
</body>
</html>
